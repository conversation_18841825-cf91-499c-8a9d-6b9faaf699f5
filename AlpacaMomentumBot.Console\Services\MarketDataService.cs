using Alpaca.Markets;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace AlpacaMomentumBot.Services;

public sealed class MarketDataService : IMarketDataService
{
    private readonly IAlpacaClientFactory _alpacaFactory;
    private readonly IPolygonClientFactory _polygonFactory;
    private readonly ILogger<MarketDataService> _logger;

    public MarketDataService(
        IAlpacaClientFactory alpacaFactory,
        IPolygonClientFactory polygonFactory,
        ILogger<MarketDataService> logger)
    {
        _alpacaFactory = alpacaFactory;
        _polygonFactory = polygonFactory;
        _logger = logger;
    }

    public async Task<IPage<IBar>> GetStockBarsAsync(string symbol, DateTime startDate, DateTime endDate)
    {
        try
        {
            using var dataClient = _alpacaFactory.CreateDataClient();
            
            var request = new HistoricalBarsRequest(symbol, BarTimeFrame.Day,
                new Interval<DateTime>(startDate, endDate));

            var response = await dataClient.ListHistoricalBarsAsync(request);
            
            _logger.LogDebug("Retrieved {Count} bars for {Symbol} from Alpaca", 
                response.Items.Count(), symbol);
            
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving stock bars for {Symbol} from Alpaca", symbol);
            throw;
        }
    }

    public async Task<IDictionary<string, IPage<IBar>>> GetStockBarsAsync(IEnumerable<string> symbols, DateTime startDate, DateTime endDate)
    {
        var results = new Dictionary<string, IPage<IBar>>();
        
        foreach (var symbol in symbols)
        {
            try
            {
                var bars = await GetStockBarsAsync(symbol, startDate, endDate);
                results[symbol] = bars;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to retrieve bars for {Symbol}, skipping", symbol);
            }
        }
        
        return results;
    }

    public async Task<decimal?> GetIndexValueAsync(string indexSymbol)
    {
        try
        {
            using var httpClient = _polygonFactory.CreateClient();
            
            // Use Polygon's real-time quote endpoint for indices
            var url = $"v2/last/trade/{indexSymbol}";
            var response = await httpClient.GetAsync(url);
            
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning("Polygon API returned {StatusCode} for index {Symbol}", 
                    response.StatusCode, indexSymbol);
                return null;
            }
            
            var content = await response.Content.ReadAsStringAsync();
            var jsonDoc = JsonDocument.Parse(content);
            
            if (jsonDoc.RootElement.TryGetProperty("results", out var results) &&
                results.TryGetProperty("p", out var priceElement))
            {
                var price = priceElement.GetDecimal();
                _logger.LogDebug("Retrieved index value for {Symbol}: {Price}", indexSymbol, price);
                return price;
            }
            
            _logger.LogWarning("Could not parse index value from Polygon response for {Symbol}", indexSymbol);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving index value for {Symbol} from Polygon", indexSymbol);
            return null;
        }
    }

    public async Task<IEnumerable<IndexBar>> GetIndexBarsAsync(string indexSymbol, DateTime startDate, DateTime endDate)
    {
        try
        {
            using var httpClient = _polygonFactory.CreateClient();
            
            // Format dates for Polygon API (YYYY-MM-DD)
            var startDateStr = startDate.ToString("yyyy-MM-dd");
            var endDateStr = endDate.ToString("yyyy-MM-dd");
            
            // Use Polygon's aggregates endpoint for historical index data
            var url = $"v2/aggs/ticker/{indexSymbol}/range/1/day/{startDateStr}/{endDateStr}?adjusted=true&sort=asc";
            var response = await httpClient.GetAsync(url);
            
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning("Polygon API returned {StatusCode} for index bars {Symbol}", 
                    response.StatusCode, indexSymbol);
                return Enumerable.Empty<IndexBar>();
            }
            
            var content = await response.Content.ReadAsStringAsync();
            var jsonDoc = JsonDocument.Parse(content);
            
            var bars = new List<IndexBar>();
            
            if (jsonDoc.RootElement.TryGetProperty("results", out var results) && results.ValueKind == JsonValueKind.Array)
            {
                foreach (var bar in results.EnumerateArray())
                {
                    if (TryParseIndexBar(bar, out var indexBar))
                    {
                        bars.Add(indexBar);
                    }
                }
            }
            
            _logger.LogDebug("Retrieved {Count} index bars for {Symbol} from Polygon", bars.Count, indexSymbol);
            return bars;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving index bars for {Symbol} from Polygon", indexSymbol);
            return Enumerable.Empty<IndexBar>();
        }
    }

    private static bool TryParseIndexBar(JsonElement barElement, out IndexBar indexBar)
    {
        indexBar = default;
        
        try
        {
            if (!barElement.TryGetProperty("t", out var timestampElement) ||
                !barElement.TryGetProperty("o", out var openElement) ||
                !barElement.TryGetProperty("h", out var highElement) ||
                !barElement.TryGetProperty("l", out var lowElement) ||
                !barElement.TryGetProperty("c", out var closeElement) ||
                !barElement.TryGetProperty("v", out var volumeElement))
            {
                return false;
            }

            var timestamp = DateTimeOffset.FromUnixTimeMilliseconds(timestampElement.GetInt64()).UtcDateTime;
            var open = openElement.GetDecimal();
            var high = highElement.GetDecimal();
            var low = lowElement.GetDecimal();
            var close = closeElement.GetDecimal();
            var volume = volumeElement.GetInt64();

            indexBar = new IndexBar(timestamp, open, high, low, close, volume);
            return true;
        }
        catch
        {
            return false;
        }
    }
}
