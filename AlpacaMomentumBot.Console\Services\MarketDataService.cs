using Alpaca.Markets;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace AlpacaMomentumBot.Services;

public sealed class MarketDataService : IMarketDataService
{
    private readonly IAlpacaClientFactory _alpacaFactory;
    private readonly IPolygonClientFactory _polygonFactory;
    private readonly ILogger<MarketDataService> _logger;

    public MarketDataService(
        IAlpacaClientFactory alpacaFactory,
        IPolygonClientFactory polygonFactory,
        ILogger<MarketDataService> logger)
    {
        _alpacaFactory = alpacaFactory;
        _polygonFactory = polygonFactory;
        _logger = logger;
    }

    // === Historical Data Methods ===

    public async Task<IPage<IBar>> GetStockBarsAsync(string symbol, DateTime startDate, DateTime endDate)
    {
        return await GetStockBarsInternalAsync(symbol, BarTimeFrame.Day, startDate, endDate);
    }

    public async Task<IPage<IBar>> GetStockMinuteBarsAsync(string symbol, DateTime startDate, DateTime endDate)
    {
        return await GetStockBarsInternalAsync(symbol, BarTimeFrame.Minute, startDate, endDate);
    }

    private async Task<IPage<IBar>> GetStockBarsInternalAsync(string symbol, BarTimeFrame timeFrame, DateTime startDate, DateTime endDate)
    {
        try
        {
            using var dataClient = _alpacaFactory.CreateDataClient();

            var request = new HistoricalBarsRequest(symbol, timeFrame,
                new Interval<DateTime>(startDate, endDate));

            var response = await dataClient.ListHistoricalBarsAsync(request);

            _logger.LogDebug("Retrieved {Count} {TimeFrame} bars for {Symbol} from Alpaca",
                response.Items.Count(), timeFrame, symbol);

            return response;
        }
        catch (Exception ex) when (ex.Message.Contains("TooManyRequests") || ex.Message.Contains("429"))
        {
            _logger.LogWarning("Alpaca throttle detected for {Symbol}, attempting Polygon fallback", symbol);

            // Fallback to Polygon for minute data if available
            if (timeFrame == BarTimeFrame.Minute)
            {
                return await GetStockBarsFromPolygonFallbackAsync(symbol, startDate, endDate);
            }
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving {TimeFrame} bars for {Symbol} from Alpaca", timeFrame, symbol);
            throw;
        }
    }

    private async Task<IPage<IBar>> GetStockBarsFromPolygonFallbackAsync(string symbol, DateTime startDate, DateTime endDate)
    {
        try
        {
            using var httpClient = _polygonFactory.CreateClient();

            var startDateStr = startDate.ToString("yyyy-MM-dd");
            var endDateStr = endDate.ToString("yyyy-MM-dd");

            // Use Polygon's aggregates endpoint for minute bars
            var url = $"v2/aggs/ticker/{symbol}/range/1/minute/{startDateStr}/{endDateStr}?adjusted=true&sort=asc&limit=50000";
            var response = await httpClient.GetAsync(url);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning("Polygon fallback failed with {StatusCode} for {Symbol}", response.StatusCode, symbol);
                throw new InvalidOperationException($"Polygon fallback failed for {symbol}");
            }

            var content = await response.Content.ReadAsStringAsync();
            var jsonDoc = JsonDocument.Parse(content);

            var bars = new List<PolygonBar>();

            if (jsonDoc.RootElement.TryGetProperty("results", out var results) && results.ValueKind == JsonValueKind.Array)
            {
                foreach (var bar in results.EnumerateArray())
                {
                    if (TryParsePolygonBar(bar, symbol, out var polygonBar))
                    {
                        bars.Add(polygonBar);
                    }
                }
            }

            _logger.LogInformation("Polygon fallback successful: Retrieved {Count} minute bars for {Symbol}", bars.Count, symbol);

            // Convert to Alpaca-compatible format
            return new PolygonBarPage(bars, symbol);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in Polygon fallback for {Symbol}", symbol);
            throw;
        }
    }

    public async Task<IDictionary<string, IPage<IBar>>> GetStockBarsAsync(IEnumerable<string> symbols, DateTime startDate, DateTime endDate)
    {
        return await GetMultipleStockBarsAsync(symbols, BarTimeFrame.Day, startDate, endDate);
    }

    public async Task<IDictionary<string, IPage<IBar>>> GetStockMinuteBarsAsync(IEnumerable<string> symbols, DateTime startDate, DateTime endDate)
    {
        return await GetMultipleStockBarsAsync(symbols, BarTimeFrame.Minute, startDate, endDate);
    }

    private async Task<IDictionary<string, IPage<IBar>>> GetMultipleStockBarsAsync(IEnumerable<string> symbols, BarTimeFrame timeFrame, DateTime startDate, DateTime endDate)
    {
        var results = new Dictionary<string, IPage<IBar>>();
        var symbolList = symbols.ToList();

        _logger.LogInformation("Retrieving {TimeFrame} bars for {Count} symbols", timeFrame, symbolList.Count);

        // Process in batches to avoid overwhelming APIs
        const int batchSize = 10;
        for (int i = 0; i < symbolList.Count; i += batchSize)
        {
            var batch = symbolList.Skip(i).Take(batchSize);
            var batchTasks = batch.Select(async symbol =>
            {
                try
                {
                    var bars = timeFrame == BarTimeFrame.Day
                        ? await GetStockBarsAsync(symbol, startDate, endDate)
                        : await GetStockMinuteBarsAsync(symbol, startDate, endDate);
                    return new { Symbol = symbol, Bars = bars, Success = true };
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to retrieve {TimeFrame} bars for {Symbol}, skipping", timeFrame, symbol);
                    return new { Symbol = symbol, Bars = (IPage<IBar>)null!, Success = false };
                }
            });

            var batchResults = await Task.WhenAll(batchTasks);

            foreach (var result in batchResults.Where(r => r.Success))
            {
                results[result.Symbol] = result.Bars;
            }

            // Add small delay between batches to be respectful to APIs
            if (i + batchSize < symbolList.Count)
            {
                await Task.Delay(100);
            }
        }

        _logger.LogInformation("Successfully retrieved {TimeFrame} bars for {SuccessCount}/{TotalCount} symbols",
            timeFrame, results.Count, symbolList.Count);

        return results;
    }

    // === Account & Positions Methods ===

    public async Task<IAccount> GetAccountAsync()
    {
        try
        {
            using var tradingClient = _alpacaFactory.CreateTradingClient();
            var account = await tradingClient.GetAccountAsync();

            _logger.LogDebug("Retrieved account information: Equity={Equity:C}, BuyingPower={BuyingPower:C}",
                account.Equity, account.BuyingPower);

            return account;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving account information from Alpaca");
            throw;
        }
    }

    public async Task<IReadOnlyList<IPosition>> GetPositionsAsync()
    {
        try
        {
            using var tradingClient = _alpacaFactory.CreateTradingClient();
            var positions = await tradingClient.ListPositionsAsync();
            var positionList = positions.ToList();

            _logger.LogDebug("Retrieved {Count} positions from Alpaca", positionList.Count);

            return positionList;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving positions from Alpaca");
            throw;
        }
    }

    public async Task<IReadOnlyList<IOrder>> GetRecentFillsAsync(int limitCount = 100)
    {
        try
        {
            using var tradingClient = _alpacaFactory.CreateTradingClient();

            var request = new ListOrdersRequest
            {
                OrderStatusFilter = OrderStatusFilter.All, // Use All instead of Filled
                LimitOrderNumber = limitCount,
                OrderListSorting = SortDirection.Descending // Most recent first
            };

            var orders = await tradingClient.ListOrdersAsync(request);
            var orderList = orders.ToList();

            _logger.LogDebug("Retrieved {Count} recent fills from Alpaca", orderList.Count);

            return orderList;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving recent fills from Alpaca");
            throw;
        }
    }

    // === Index Data Methods ===

    public async Task<decimal?> GetIndexValueAsync(string indexSymbol)
    {
        try
        {
            using var httpClient = _polygonFactory.CreateClient();
            
            // Use Polygon's real-time quote endpoint for indices
            var url = $"v2/last/trade/{indexSymbol}";
            var response = await httpClient.GetAsync(url);
            
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning("Polygon API returned {StatusCode} for index {Symbol}", 
                    response.StatusCode, indexSymbol);
                return null;
            }
            
            var content = await response.Content.ReadAsStringAsync();
            var jsonDoc = JsonDocument.Parse(content);
            
            if (jsonDoc.RootElement.TryGetProperty("results", out var results) &&
                results.TryGetProperty("p", out var priceElement))
            {
                var price = priceElement.GetDecimal();
                _logger.LogDebug("Retrieved index value for {Symbol}: {Price}", indexSymbol, price);
                return price;
            }
            
            _logger.LogWarning("Could not parse index value from Polygon response for {Symbol}", indexSymbol);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving index value for {Symbol} from Polygon", indexSymbol);
            return null;
        }
    }

    public async Task<IEnumerable<IndexBar>> GetIndexBarsAsync(string indexSymbol, DateTime startDate, DateTime endDate)
    {
        try
        {
            using var httpClient = _polygonFactory.CreateClient();
            
            // Format dates for Polygon API (YYYY-MM-DD)
            var startDateStr = startDate.ToString("yyyy-MM-dd");
            var endDateStr = endDate.ToString("yyyy-MM-dd");
            
            // Use Polygon's aggregates endpoint for historical index data
            var url = $"v2/aggs/ticker/{indexSymbol}/range/1/day/{startDateStr}/{endDateStr}?adjusted=true&sort=asc";
            var response = await httpClient.GetAsync(url);
            
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning("Polygon API returned {StatusCode} for index bars {Symbol}", 
                    response.StatusCode, indexSymbol);
                return Enumerable.Empty<IndexBar>();
            }
            
            var content = await response.Content.ReadAsStringAsync();
            var jsonDoc = JsonDocument.Parse(content);
            
            var bars = new List<IndexBar>();
            
            if (jsonDoc.RootElement.TryGetProperty("results", out var results) && results.ValueKind == JsonValueKind.Array)
            {
                foreach (var bar in results.EnumerateArray())
                {
                    if (TryParseIndexBar(bar, out var indexBar))
                    {
                        bars.Add(indexBar);
                    }
                }
            }
            
            _logger.LogDebug("Retrieved {Count} index bars for {Symbol} from Polygon", bars.Count, indexSymbol);
            return bars;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving index bars for {Symbol} from Polygon", indexSymbol);
            return Enumerable.Empty<IndexBar>();
        }
    }

    // === Options Data Methods ===

    public async Task<IEnumerable<OptionData>> GetOptionsDataAsync(string underlyingSymbol, DateTime? expirationDate = null)
    {
        try
        {
            using var httpClient = _polygonFactory.CreateClient();

            // Build URL for options data
            var url = $"v3/reference/options/contracts?underlying_ticker={underlyingSymbol}&limit=1000";

            if (expirationDate.HasValue)
            {
                url += $"&expiration_date={expirationDate.Value:yyyy-MM-dd}";
            }

            var response = await httpClient.GetAsync(url);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning("Polygon options API returned {StatusCode} for {Symbol}", response.StatusCode, underlyingSymbol);
                return Enumerable.Empty<OptionData>();
            }

            var content = await response.Content.ReadAsStringAsync();
            var jsonDoc = JsonDocument.Parse(content);

            var options = new List<OptionData>();

            if (jsonDoc.RootElement.TryGetProperty("results", out var results) && results.ValueKind == JsonValueKind.Array)
            {
                foreach (var option in results.EnumerateArray())
                {
                    if (TryParseOptionData(option, out var optionData))
                    {
                        options.Add(optionData);
                    }
                }
            }

            _logger.LogDebug("Retrieved {Count} options contracts for {Symbol} from Polygon", options.Count, underlyingSymbol);
            return options;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving options data for {Symbol} from Polygon", underlyingSymbol);
            return Enumerable.Empty<OptionData>();
        }
    }

    public async Task<IEnumerable<VixTermData>> GetVixTermStructureAsync()
    {
        try
        {
            using var httpClient = _polygonFactory.CreateClient();

            // Get VIX futures data for term structure
            var url = "v3/reference/options/contracts?underlying_ticker=VIX&contract_type=future&limit=100";
            var response = await httpClient.GetAsync(url);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning("Polygon VIX term structure API returned {StatusCode}", response.StatusCode);
                return Enumerable.Empty<VixTermData>();
            }

            var content = await response.Content.ReadAsStringAsync();
            var jsonDoc = JsonDocument.Parse(content);

            var termData = new List<VixTermData>();

            if (jsonDoc.RootElement.TryGetProperty("results", out var results) && results.ValueKind == JsonValueKind.Array)
            {
                foreach (var contract in results.EnumerateArray())
                {
                    if (TryParseVixTermData(contract, out var vixData))
                    {
                        termData.Add(vixData);
                    }
                }
            }

            _logger.LogDebug("Retrieved {Count} VIX term structure points from Polygon", termData.Count);
            return termData.OrderBy(v => v.ExpirationDate);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving VIX term structure from Polygon");
            return Enumerable.Empty<VixTermData>();
        }
    }

    // === Helper Methods ===

    private static bool TryParseIndexBar(JsonElement barElement, out IndexBar indexBar)
    {
        indexBar = default;

        try
        {
            if (!barElement.TryGetProperty("t", out var timestampElement) ||
                !barElement.TryGetProperty("o", out var openElement) ||
                !barElement.TryGetProperty("h", out var highElement) ||
                !barElement.TryGetProperty("l", out var lowElement) ||
                !barElement.TryGetProperty("c", out var closeElement) ||
                !barElement.TryGetProperty("v", out var volumeElement))
            {
                return false;
            }

            var timestamp = DateTimeOffset.FromUnixTimeMilliseconds(timestampElement.GetInt64()).UtcDateTime;
            var open = openElement.GetDecimal();
            var high = highElement.GetDecimal();
            var low = lowElement.GetDecimal();
            var close = closeElement.GetDecimal();
            var volume = volumeElement.GetInt64();

            indexBar = new IndexBar(timestamp, open, high, low, close, volume);
            return true;
        }
        catch
        {
            return false;
        }
    }

    private static bool TryParsePolygonBar(JsonElement barElement, string symbol, out PolygonBar polygonBar)
    {
        polygonBar = default;

        try
        {
            if (!barElement.TryGetProperty("t", out var timestampElement) ||
                !barElement.TryGetProperty("o", out var openElement) ||
                !barElement.TryGetProperty("h", out var highElement) ||
                !barElement.TryGetProperty("l", out var lowElement) ||
                !barElement.TryGetProperty("c", out var closeElement) ||
                !barElement.TryGetProperty("v", out var volumeElement))
            {
                return false;
            }

            var timestamp = DateTimeOffset.FromUnixTimeMilliseconds(timestampElement.GetInt64()).UtcDateTime;
            var open = openElement.GetDecimal();
            var high = highElement.GetDecimal();
            var low = lowElement.GetDecimal();
            var close = closeElement.GetDecimal();
            var volume = volumeElement.GetInt64();

            polygonBar = new PolygonBar(symbol, timestamp, open, high, low, close, volume);
            return true;
        }
        catch
        {
            return false;
        }
    }

    private static bool TryParseOptionData(JsonElement optionElement, out OptionData optionData)
    {
        optionData = default;

        try
        {
            if (!optionElement.TryGetProperty("ticker", out var tickerElement) ||
                !optionElement.TryGetProperty("underlying_ticker", out var underlyingElement))
            {
                return false;
            }

            var symbol = tickerElement.GetString() ?? "";
            var underlying = underlyingElement.GetString() ?? "";

            // Parse expiration date
            var expirationDate = DateTime.MinValue;
            if (optionElement.TryGetProperty("expiration_date", out var expElement))
            {
                DateTime.TryParse(expElement.GetString(), out expirationDate);
            }

            // Parse strike price
            var strike = 0m;
            if (optionElement.TryGetProperty("strike_price", out var strikeElement))
            {
                strike = strikeElement.GetDecimal();
            }

            // Parse option type
            var optionType = "";
            if (optionElement.TryGetProperty("contract_type", out var typeElement))
            {
                optionType = typeElement.GetString() ?? "";
            }

            optionData = new OptionData(
                symbol, underlying, expirationDate, strike, optionType,
                null, null, null, null, null, null, null, null, null, null);

            return true;
        }
        catch
        {
            return false;
        }
    }

    private static bool TryParseVixTermData(JsonElement contractElement, out VixTermData vixData)
    {
        vixData = default;

        try
        {
            if (!contractElement.TryGetProperty("expiration_date", out var expElement))
            {
                return false;
            }

            if (!DateTime.TryParse(expElement.GetString(), out var expirationDate))
            {
                return false;
            }

            // For now, we'll need to get the price separately
            // This is a simplified implementation
            var price = 0m;
            var daysToExpiration = (int)(expirationDate - DateTime.UtcNow).TotalDays;

            vixData = new VixTermData(expirationDate, price, daysToExpiration);
            return true;
        }
        catch
        {
            return false;
        }
    }
}
