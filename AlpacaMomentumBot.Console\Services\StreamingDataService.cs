using Alpaca.Markets;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace AlpacaMomentumBot.Services;

/// <summary>
/// Implementation of real-time streaming data service combining Alpaca and Polygon websockets
/// </summary>
public sealed class StreamingDataService : IStreamingDataService
{
    private readonly IAlpacaClientFactory _alpacaFactory;
    private readonly IPolygonClientFactory _polygonFactory;
    private readonly ILogger<StreamingDataService> _logger;
    
    private IAlpacaStreamingClient? _alpacaStreamingClient;
    private IAlpacaDataStreamingClient? _alpacaDataStreamingClient;
    private bool _disposed;

    public StreamingDataService(
        IAlpacaClientFactory alpacaFactory,
        IPolygonClientFactory polygonFactory,
        ILogger<StreamingDataService> logger)
    {
        _alpacaFactory = alpacaFactory;
        _polygonFactory = polygonFactory;
        _logger = logger;
        ConnectionStatus = StreamingConnectionStatus.Disconnected;
    }

    // === Events ===
    
    public event EventHandler<StreamingQuoteEventArgs>? QuoteReceived;
    public event EventHandler<StreamingBarEventArgs>? BarReceived;
    public event EventHandler<IndexUpdateEventArgs>? IndexUpdated;
    public event EventHandler<TradeUpdateEventArgs>? TradeUpdated;

    // === Properties ===
    
    public StreamingConnectionStatus ConnectionStatus { get; private set; }

    // === Connection Management ===

    public async Task ConnectAlpacaStreamAsync()
    {
        try
        {
            ConnectionStatus = StreamingConnectionStatus.Connecting;
            _logger.LogInformation("Connecting to Alpaca streaming services...");

            // Create streaming clients
            _alpacaStreamingClient = _alpacaFactory.CreateTradingClient().GetStreamingClient();
            _alpacaDataStreamingClient = _alpacaFactory.CreateDataClient().GetStreamingClient();

            // Set up event handlers
            SetupAlpacaEventHandlers();

            // Connect to both streaming services
            await _alpacaStreamingClient.ConnectAndAuthenticateAsync();
            await _alpacaDataStreamingClient.ConnectAndAuthenticateAsync();

            ConnectionStatus = StreamingConnectionStatus.Connected;
            _logger.LogInformation("Successfully connected to Alpaca streaming services");
        }
        catch (Exception ex)
        {
            ConnectionStatus = StreamingConnectionStatus.Error;
            _logger.LogError(ex, "Failed to connect to Alpaca streaming services");
            throw;
        }
    }

    public async Task ConnectPolygonStreamAsync()
    {
        try
        {
            _logger.LogInformation("Polygon streaming connection would be implemented here");
            // TODO: Implement Polygon websocket connection
            // This would require additional Polygon websocket client setup
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to connect to Polygon streaming services");
            throw;
        }
    }

    public async Task DisconnectAllAsync()
    {
        try
        {
            _logger.LogInformation("Disconnecting from all streaming services...");

            if (_alpacaStreamingClient != null)
            {
                await _alpacaStreamingClient.DisconnectAsync();
                _alpacaStreamingClient.Dispose();
                _alpacaStreamingClient = null;
            }

            if (_alpacaDataStreamingClient != null)
            {
                await _alpacaDataStreamingClient.DisconnectAsync();
                _alpacaDataStreamingClient.Dispose();
                _alpacaDataStreamingClient = null;
            }

            ConnectionStatus = StreamingConnectionStatus.Disconnected;
            _logger.LogInformation("Disconnected from all streaming services");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during streaming disconnect");
        }
    }

    // === Subscription Management ===

    public async Task SubscribeToQuotesAsync(IEnumerable<string> symbols)
    {
        if (_alpacaDataStreamingClient == null)
        {
            throw new InvalidOperationException("Alpaca data streaming client not connected");
        }

        var symbolList = symbols.ToList();
        _logger.LogInformation("Subscribing to quotes for {Count} symbols: {Symbols}", 
            symbolList.Count, string.Join(", ", symbolList));

        await _alpacaDataStreamingClient.SubscribeAsync(symbolList.Select(s => new QuoteSubscription(s)));
    }

    public async Task SubscribeToBarsAsync(IEnumerable<string> symbols)
    {
        if (_alpacaDataStreamingClient == null)
        {
            throw new InvalidOperationException("Alpaca data streaming client not connected");
        }

        var symbolList = symbols.ToList();
        _logger.LogInformation("Subscribing to bars for {Count} symbols: {Symbols}", 
            symbolList.Count, string.Join(", ", symbolList));

        await _alpacaDataStreamingClient.SubscribeAsync(symbolList.Select(s => new BarSubscription(s)));
    }

    public async Task SubscribeToIndexUpdatesAsync(IEnumerable<string> indexSymbols)
    {
        var symbolList = indexSymbols.ToList();
        _logger.LogInformation("Would subscribe to index updates for: {Symbols}", string.Join(", ", symbolList));
        
        // TODO: Implement Polygon index subscriptions
        await Task.CompletedTask;
    }

    public async Task SubscribeToTradeUpdatesAsync()
    {
        if (_alpacaStreamingClient == null)
        {
            throw new InvalidOperationException("Alpaca streaming client not connected");
        }

        _logger.LogInformation("Subscribing to trade updates");
        await _alpacaStreamingClient.SubscribeAsync(new TradeUpdateSubscription());
    }

    public async Task UnsubscribeAllAsync()
    {
        try
        {
            if (_alpacaDataStreamingClient != null)
            {
                await _alpacaDataStreamingClient.UnsubscribeAsync(new IStreamingSubscription[] { });
            }

            if (_alpacaStreamingClient != null)
            {
                await _alpacaStreamingClient.UnsubscribeAsync(new IStreamingSubscription[] { });
            }

            _logger.LogInformation("Unsubscribed from all streaming data");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unsubscribing from streaming data");
        }
    }

    // === Private Methods ===

    private void SetupAlpacaEventHandlers()
    {
        if (_alpacaDataStreamingClient != null)
        {
            _alpacaDataStreamingClient.QuoteReceived += OnQuoteReceived;
            _alpacaDataStreamingClient.BarReceived += OnBarReceived;
        }

        if (_alpacaStreamingClient != null)
        {
            _alpacaStreamingClient.TradeUpdateReceived += OnTradeUpdateReceived;
        }
    }

    private void OnQuoteReceived(IQuote quote)
    {
        try
        {
            var args = new StreamingQuoteEventArgs
            {
                Symbol = quote.Symbol,
                BidPrice = quote.BidPrice,
                AskPrice = quote.AskPrice,
                BidSize = quote.BidSize,
                AskSize = quote.AskSize,
                Timestamp = quote.TimestampUtc.DateTime
            };

            QuoteReceived?.Invoke(this, args);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing quote for {Symbol}", quote.Symbol);
        }
    }

    private void OnBarReceived(IBar bar)
    {
        try
        {
            var args = new StreamingBarEventArgs
            {
                Symbol = bar.Symbol,
                Open = bar.Open,
                High = bar.High,
                Low = bar.Low,
                Close = bar.Close,
                Volume = (long)bar.Volume,
                Timestamp = bar.TimeUtc.DateTime
            };

            BarReceived?.Invoke(this, args);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing bar for {Symbol}", bar.Symbol);
        }
    }

    private void OnTradeUpdateReceived(ITradeUpdate tradeUpdate)
    {
        try
        {
            var args = new TradeUpdateEventArgs
            {
                Symbol = tradeUpdate.Order.Symbol,
                OrderId = tradeUpdate.Order.OrderId.ToString(),
                Quantity = tradeUpdate.Order.Quantity ?? 0,
                Price = tradeUpdate.Price ?? 0,
                Side = tradeUpdate.Order.OrderSide.ToString().ToLowerInvariant(),
                Status = tradeUpdate.Event.ToString().ToLowerInvariant(),
                Timestamp = tradeUpdate.TimestampUtc.DateTime
            };

            TradeUpdated?.Invoke(this, args);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing trade update for order {OrderId}", tradeUpdate.Order.OrderId);
        }
    }

    // === IDisposable ===

    public void Dispose()
    {
        if (!_disposed)
        {
            DisconnectAllAsync().GetAwaiter().GetResult();
            _disposed = true;
        }
    }
}
