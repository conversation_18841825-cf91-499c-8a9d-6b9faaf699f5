using Microsoft.Extensions.Configuration;

namespace AlpacaMomentumBot.Services;

public sealed class PolygonClientFactory : IPolygonClientFactory
{
    private readonly HttpClient _client;

    public PolygonClientFactory(IHttpClientFactory httpFactory, IConfiguration config)
    {
        _client = httpFactory.CreateClient("polygon");
        _client.BaseAddress = new Uri("https://api.polygon.io/");
        
        var apiKey = config["POLY_API_KEY"];
        if (!string.IsNullOrEmpty(apiKey))
        {
            _client.DefaultRequestHeaders.Add("Authorization", $"Bearer {apiKey}");
        }
    }

    public HttpClient CreateClient() => _client;
}
