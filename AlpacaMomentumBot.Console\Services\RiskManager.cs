using Alpaca.Markets;
using Microsoft.Extensions.Logging;

namespace AlpacaMomentumBot.Services;

public sealed class RiskManager : IRiskManager
{
    private readonly IAlpacaClientFactory _clientFactory;
    private readonly ILogger<RiskManager> _logger;

    public RiskManager(IAlpacaClientFactory clientFactory, ILogger<RiskManager> logger)
    {
        _clientFactory = clientFactory;
        _logger = logger;
    }

    public async Task<decimal> CalculateQuantityAsync(TradingSignal signal)
    {
        try
        {
            using var tradingClient = _clientFactory.CreateTradingClient();
            
            // Get account information
            var account = await tradingClient.GetAccountAsync();
            
            // Calculate risk dollars: min(account.Equity * 0.01m, 1000m) for 10bps per $100k cap
            var equity = account.Equity ?? 0m;
            var riskDollars = Math.Min(equity * 0.01m, 1000m);
            
            // Calculate quantity: qty = riskDollars / (atr14 * price)
            var quantity = riskDollars / (signal.Atr * signal.Price);
            
            // Ensure quantity doesn't exceed risk tolerance: qty <= riskDollars / price
            var maxQuantity = riskDollars / signal.Price;
            quantity = Math.Min(quantity, maxQuantity);
            
            // Round down to avoid over-allocation
            quantity = Math.Floor(quantity * 100) / 100; // Round to 2 decimal places
            
            _logger.LogInformation("Risk calculation for {Symbol}: Equity={Equity:C}, RiskDollars={RiskDollars:C}, " +
                                 "Price={Price:C}, ATR={ATR:C}, Quantity={Quantity}",
                signal.Symbol, equity, riskDollars, signal.Price, signal.Atr, quantity);

            return Math.Max(0, quantity);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating position size for {Symbol}", signal.Symbol);
            return 0;
        }
    }
}
