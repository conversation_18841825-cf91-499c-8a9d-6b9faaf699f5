# SmaTrendFollower

A .NET 8 console application that implements an SMA-following momentum trading strategy using the Alpaca Markets API.

## Features

- **Single-Shot Execution**: Manual one-shot execution flow with market session guard
- **Universe Screening**: Screens SPY + top-500 tickers with SMA50/SMA200 filtering
- **Portfolio Gate**: SPY SMA200 check to determine if trading should occur
- **Risk Management**: 10bps per $100k equity cap with ATR-based position sizing
- **Trade Execution**: Limit-on-Open pattern with 2×ATR stop-loss orders
- **Comprehensive Logging**: Uses Serilog for console and file logging with daily rolling logs
- **Dependency Injection**: Clean architecture with scoped services for each trading cycle
- **Comprehensive Testing**: Unit tests with xUnit, FluentAssertions, and Moq

## Architecture

The application follows a clean architecture pattern with the following services:

- **MarketSessionGuard**: Validates trading is allowed (weekdays only)
- **TradingService**: Orchestrates the complete trading cycle
- **SignalGenerator**: Universe screening with SPY + top-500 tickers, SMA filtering
- **PortfolioGate**: SPY SMA200 check to gate trading decisions
- **RiskManager**: 10bps per $100k cap with ATR-based position sizing
- **TradeExecutor**: Limit-on-Open + 2×ATR stop-loss execution pattern
- **AlpacaClientFactory**: Creates and manages Alpaca API clients
- **PolygonClientFactory**: Creates and manages Polygon HTTP clients
- **MarketDataService**: Unified interface for Alpaca + Polygon data sources
- **UniverseProvider**: Provides symbol universe from file or defaults

## Setup

### Prerequisites

- .NET 8 SDK
- Alpaca Markets account (paper or live)
- Polygon.io API key (optional, for index data like SPX, VIX)

### Configuration

1. Copy `.env.example` to `.env`:
   ```bash
   cp AlpacaMomentumBot.Console/.env.example AlpacaMomentumBot.Console/.env
   ```

2. Update the `.env` file with your credentials:
   ```
   # Alpaca credentials (required)
   APCA_API_KEY_ID=your_alpaca_api_key_here
   APCA_API_SECRET_KEY=your_alpaca_secret_key_here
   APCA_API_ENV=paper  # or live for real trading

   # Polygon credentials (optional, for index data like SPX, VIX)
   POLY_API_KEY=your_polygon_api_key_here
   ```

### Running the Application

```bash
# Build the solution
dotnet build AlpacaMomentumBot.sln

# Run the application
dotnet run --project AlpacaMomentumBot.Console

# Run tests
dotnet test AlpacaMomentumBot.sln
```

## Trading Strategy

The bot implements an SMA-following momentum strategy with universe screening:

### Signal Generation
- **Universe**: SPY + top-500 tickers from universe.csv or defaults
- **Filtering**: close > SMA50 && close > SMA200 && ATR/close < 3%
- **Ranking**: Ranked by 6-month return descending
- **Selection**: Top N symbols (default 10)

### Portfolio Gate
- **SPY SMA200 Check**: Only trade when SPY close > SPY SMA200
- **Market Session**: Only trade on weekdays (no weekends)

### Risk Management
- **Risk Capital**: min(account equity × 1%, $1000) - 10bps per $100k cap
- **Position Sizing**: quantity = riskDollars / (ATR14 × price)
- **Max Position**: quantity ≤ riskDollars / price

### Trade Execution
- **Entry**: Limit-on-Open at lastClose × 1.002
- **Stop Loss**: GTC stop at entry - 2×ATR14
- **Order Management**: Cancel existing orders before new trades

### Default Universe

The bot uses these symbols by default (when universe.csv is not found):
- SPY (S&P 500 ETF)
- QQQ (NASDAQ ETF)
- AAPL (Apple)
- MSFT (Microsoft)
- NVDA (NVIDIA)

Create a `universe.csv` file in the project root with one symbol per line to customize the universe.

## Market Data Integration

The bot uses a unified `MarketDataService` that combines multiple data sources:

### Data Sources
- **Alpaca Markets**: Stock and ETF data (AAPL, SPY, QQQ, etc.)
- **Polygon.io**: Index data (SPX, VIX, DJI, etc.)

### Usage Examples
```csharp
// Get stock data from Alpaca
var stockBars = await marketDataService.GetStockBarsAsync("AAPL", startDate, endDate);

// Get multiple stocks from Alpaca
var symbols = new[] { "SPY", "QQQ", "MSFT" };
var allBars = await marketDataService.GetStockBarsAsync(symbols, startDate, endDate);

// Get current index value from Polygon
var spxValue = await marketDataService.GetIndexValueAsync("I:SPX");

// Get historical index data from Polygon
var vixBars = await marketDataService.GetIndexBarsAsync("I:VIX", startDate, endDate);
```

### Index Symbols (Polygon)
- `I:SPX` - S&P 500 Index
- `I:VIX` - CBOE Volatility Index
- `I:DJI` - Dow Jones Industrial Average
- `I:NDX` - NASDAQ 100 Index

## Logging

Logs are written to:
- Console (real-time)
- `logs/alpaca-momentum-bot-YYYY-MM-DD.log` (daily rolling files, 30-day retention)

## Execution

The bot uses a single-shot execution model:
- Run manually via `dotnet run --project AlpacaMomentumBot.Console`
- Checks market session guard (weekdays only)
- Executes complete trading cycle and exits
- Suitable for scheduled execution via external scheduler (cron, Task Scheduler, etc.)

## Safety Features

- **Paper Trading**: Set `ALPACA_PAPER=true` for safe testing
- **Position Limits**: Prevents over-concentration
- **Market Hours Check**: Only trades when markets are open
- **Error Handling**: Comprehensive exception handling and logging
- **Dry Run Capability**: Can be easily modified for simulation mode

## Development

### Project Structure

```
SmaTrendFollower/
├── AlpacaMomentumBot.Console/          # Main console application
│   ├── Services/                       # Business logic services
│   │   ├── Interfaces/                 # Service interfaces
│   │   └── Implementations/            # Service implementations
│   ├── Models/                         # Data models and primitives
│   ├── Extensions/                     # Extension methods for indicators
│   ├── Program.cs                      # Application entry point
│   └── .env.example                    # Environment variables template
├── AlpacaMomentumBot.Tests/            # Unit tests with xUnit
│   └── Services/                       # Service unit tests
├── README.md                           # This file
└── universe.csv                        # Optional symbol universe file
```

### Adding New Features

1. Create interfaces in the `Services` folder
2. Implement services with proper dependency injection
3. Register services in `Program.cs`
4. Add comprehensive unit tests
5. Update documentation

## Disclaimer

This software is for educational and research purposes only. Trading involves substantial risk of loss and is not suitable for all investors. Past performance does not guarantee future results. Always test thoroughly with paper trading before using real money.

## License

This project is licensed under the MIT License.
