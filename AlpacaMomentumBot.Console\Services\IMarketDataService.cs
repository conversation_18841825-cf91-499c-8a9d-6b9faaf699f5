using Alpaca.Markets;

namespace AlpacaMomentumBot.Services;

public interface IMarketDataService
{
    /// <summary>
    /// Gets historical bars for stock/ETF symbols from Alpaca
    /// </summary>
    Task<IPage<IBar>> GetStockBarsAsync(string symbol, DateTime startDate, DateTime endDate);
    
    /// <summary>
    /// Gets historical bars for multiple stock/ETF symbols from Alpaca
    /// </summary>
    Task<IDictionary<string, IPage<IBar>>> GetStockBarsAsync(IEnumerable<string> symbols, DateTime startDate, DateTime endDate);
    
    /// <summary>
    /// Gets index-level data (like SPX, VIX) from Polygon
    /// </summary>
    Task<decimal?> GetIndexValueAsync(string indexSymbol);
    
    /// <summary>
    /// Gets historical index data from Polygon
    /// </summary>
    Task<IEnumerable<IndexBar>> GetIndexBarsAsync(string indexSymbol, DateTime startDate, DateTime endDate);
}

/// <summary>
/// Represents a bar of index data from Polygon
/// </summary>
public readonly record struct IndexBar(
    DateTime TimeUtc,
    decimal Open,
    decimal High,
    decimal Low,
    decimal Close,
    long Volume
);
