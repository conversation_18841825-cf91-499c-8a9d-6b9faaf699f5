using AlpacaMomentumBot.Services;
using Alpaca.Markets;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace AlpacaMomentumBot.Tests.Services;

public class StreamingDataServiceTests
{
    private readonly Mock<IAlpacaClientFactory> _mockAlpacaFactory;
    private readonly Mock<IPolygonClientFactory> _mockPolygonFactory;
    private readonly Mock<ILogger<StreamingDataService>> _mockLogger;
    private readonly Mock<IAlpacaTradingClient> _mockTradingClient;
    private readonly Mock<IAlpacaDataClient> _mockDataClient;
    private readonly Mock<IAlpacaStreamingClient> _mockStreamingClient;
    private readonly Mock<IAlpacaDataStreamingClient> _mockDataStreamingClient;
    private readonly StreamingDataService _streamingService;

    public StreamingDataServiceTests()
    {
        _mockAlpacaFactory = new Mock<IAlpacaClientFactory>();
        _mockPolygonFactory = new Mock<IPolygonClientFactory>();
        _mockLogger = new Mock<ILogger<StreamingDataService>>();
        _mockTradingClient = new Mock<IAlpacaTradingClient>();
        _mockDataClient = new Mock<IAlpacaDataClient>();
        _mockStreamingClient = new Mock<IAlpacaStreamingClient>();
        _mockDataStreamingClient = new Mock<IAlpacaDataStreamingClient>();

        _mockAlpacaFactory.Setup(x => x.CreateTradingClient()).Returns(_mockTradingClient.Object);
        _mockAlpacaFactory.Setup(x => x.CreateDataClient()).Returns(_mockDataClient.Object);
        _mockTradingClient.Setup(x => x.GetStreamingClient()).Returns(_mockStreamingClient.Object);
        _mockDataClient.Setup(x => x.GetStreamingClient()).Returns(_mockDataStreamingClient.Object);

        _streamingService = new StreamingDataService(
            _mockAlpacaFactory.Object,
            _mockPolygonFactory.Object,
            _mockLogger.Object);
    }

    [Fact]
    public void Constructor_InitializesWithDisconnectedStatus()
    {
        // Assert
        _streamingService.ConnectionStatus.Should().Be(StreamingConnectionStatus.Disconnected);
    }

    [Fact]
    public async Task ConnectAlpacaStreamAsync_WhenSuccessful_SetsConnectedStatus()
    {
        // Arrange
        _mockStreamingClient.Setup(x => x.ConnectAndAuthenticateAsync(default))
            .Returns(Task.CompletedTask);
        _mockDataStreamingClient.Setup(x => x.ConnectAndAuthenticateAsync(default))
            .Returns(Task.CompletedTask);

        // Act
        await _streamingService.ConnectAlpacaStreamAsync();

        // Assert
        _streamingService.ConnectionStatus.Should().Be(StreamingConnectionStatus.Connected);
    }

    [Fact]
    public async Task ConnectAlpacaStreamAsync_WhenFails_SetsErrorStatus()
    {
        // Arrange
        _mockStreamingClient.Setup(x => x.ConnectAndAuthenticateAsync(default))
            .ThrowsAsync(new InvalidOperationException("Connection failed"));

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => 
            _streamingService.ConnectAlpacaStreamAsync());
        
        _streamingService.ConnectionStatus.Should().Be(StreamingConnectionStatus.Error);
    }

    [Fact]
    public async Task SubscribeToQuotesAsync_WhenNotConnected_ThrowsException()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT" };

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => 
            _streamingService.SubscribeToQuotesAsync(symbols));
    }

    [Fact]
    public async Task SubscribeToQuotesAsync_WhenConnected_SubscribesToSymbols()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT" };
        
        _mockStreamingClient.Setup(x => x.ConnectAndAuthenticateAsync(default))
            .Returns(Task.CompletedTask);
        _mockDataStreamingClient.Setup(x => x.ConnectAndAuthenticateAsync(default))
            .Returns(Task.CompletedTask);
        _mockDataStreamingClient.Setup(x => x.SubscribeAsync(It.IsAny<IEnumerable<QuoteSubscription>>(), default))
            .Returns(Task.CompletedTask);

        await _streamingService.ConnectAlpacaStreamAsync();

        // Act
        await _streamingService.SubscribeToQuotesAsync(symbols);

        // Assert
        _mockDataStreamingClient.Verify(x => x.SubscribeAsync(
            It.Is<IEnumerable<QuoteSubscription>>(subs => subs.Count() == 2), default), Times.Once);
    }

    [Fact]
    public async Task SubscribeToBarsAsync_WhenConnected_SubscribesToSymbols()
    {
        // Arrange
        var symbols = new[] { "SPY", "QQQ" };
        
        _mockStreamingClient.Setup(x => x.ConnectAndAuthenticateAsync(default))
            .Returns(Task.CompletedTask);
        _mockDataStreamingClient.Setup(x => x.ConnectAndAuthenticateAsync(default))
            .Returns(Task.CompletedTask);
        _mockDataStreamingClient.Setup(x => x.SubscribeAsync(It.IsAny<IEnumerable<BarSubscription>>(), default))
            .Returns(Task.CompletedTask);

        await _streamingService.ConnectAlpacaStreamAsync();

        // Act
        await _streamingService.SubscribeToBarsAsync(symbols);

        // Assert
        _mockDataStreamingClient.Verify(x => x.SubscribeAsync(
            It.Is<IEnumerable<BarSubscription>>(subs => subs.Count() == 2), default), Times.Once);
    }

    [Fact]
    public async Task SubscribeToTradeUpdatesAsync_WhenConnected_SubscribesToTradeUpdates()
    {
        // Arrange
        _mockStreamingClient.Setup(x => x.ConnectAndAuthenticateAsync(default))
            .Returns(Task.CompletedTask);
        _mockDataStreamingClient.Setup(x => x.ConnectAndAuthenticateAsync(default))
            .Returns(Task.CompletedTask);
        _mockStreamingClient.Setup(x => x.SubscribeAsync(It.IsAny<TradeUpdateSubscription>(), default))
            .Returns(Task.CompletedTask);

        await _streamingService.ConnectAlpacaStreamAsync();

        // Act
        await _streamingService.SubscribeToTradeUpdatesAsync();

        // Assert
        _mockStreamingClient.Verify(x => x.SubscribeAsync(
            It.IsAny<TradeUpdateSubscription>(), default), Times.Once);
    }

    [Fact]
    public async Task DisconnectAllAsync_DisconnectsAndDisposesClients()
    {
        // Arrange
        _mockStreamingClient.Setup(x => x.ConnectAndAuthenticateAsync(default))
            .Returns(Task.CompletedTask);
        _mockDataStreamingClient.Setup(x => x.ConnectAndAuthenticateAsync(default))
            .Returns(Task.CompletedTask);
        _mockStreamingClient.Setup(x => x.DisconnectAsync(default))
            .Returns(Task.CompletedTask);
        _mockDataStreamingClient.Setup(x => x.DisconnectAsync(default))
            .Returns(Task.CompletedTask);

        await _streamingService.ConnectAlpacaStreamAsync();

        // Act
        await _streamingService.DisconnectAllAsync();

        // Assert
        _mockStreamingClient.Verify(x => x.DisconnectAsync(default), Times.Once);
        _mockDataStreamingClient.Verify(x => x.DisconnectAsync(default), Times.Once);
        _mockStreamingClient.Verify(x => x.Dispose(), Times.Once);
        _mockDataStreamingClient.Verify(x => x.Dispose(), Times.Once);
        
        _streamingService.ConnectionStatus.Should().Be(StreamingConnectionStatus.Disconnected);
    }

    [Fact]
    public void QuoteReceived_Event_CanBeSubscribedTo()
    {
        // Arrange
        StreamingQuoteEventArgs? receivedArgs = null;
        _streamingService.QuoteReceived += (sender, args) => receivedArgs = args;

        // This test verifies the event can be subscribed to
        // In a real scenario, the event would be triggered by the Alpaca streaming client
        
        // Assert
        receivedArgs.Should().BeNull(); // No events fired yet
    }

    [Fact]
    public void BarReceived_Event_CanBeSubscribedTo()
    {
        // Arrange
        StreamingBarEventArgs? receivedArgs = null;
        _streamingService.BarReceived += (sender, args) => receivedArgs = args;

        // This test verifies the event can be subscribed to
        // In a real scenario, the event would be triggered by the Alpaca streaming client
        
        // Assert
        receivedArgs.Should().BeNull(); // No events fired yet
    }

    [Fact]
    public void TradeUpdated_Event_CanBeSubscribedTo()
    {
        // Arrange
        TradeUpdateEventArgs? receivedArgs = null;
        _streamingService.TradeUpdated += (sender, args) => receivedArgs = args;

        // This test verifies the event can be subscribed to
        // In a real scenario, the event would be triggered by the Alpaca streaming client
        
        // Assert
        receivedArgs.Should().BeNull(); // No events fired yet
    }

    [Fact]
    public void IndexUpdated_Event_CanBeSubscribedTo()
    {
        // Arrange
        IndexUpdateEventArgs? receivedArgs = null;
        _streamingService.IndexUpdated += (sender, args) => receivedArgs = args;

        // This test verifies the event can be subscribed to
        // In a real scenario, the event would be triggered by the Polygon streaming client
        
        // Assert
        receivedArgs.Should().BeNull(); // No events fired yet
    }

    [Fact]
    public void Dispose_CallsDisconnectAll()
    {
        // Arrange
        _mockStreamingClient.Setup(x => x.DisconnectAsync(default))
            .Returns(Task.CompletedTask);
        _mockDataStreamingClient.Setup(x => x.DisconnectAsync(default))
            .Returns(Task.CompletedTask);

        // Act
        _streamingService.Dispose();

        // Assert - Dispose should trigger disconnect
        // Note: In the actual implementation, this would call DisconnectAllAsync().GetAwaiter().GetResult()
    }
}
